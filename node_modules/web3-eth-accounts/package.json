{"name": "web3-eth-accounts", "version": "1.8.2", "description": "Web3 module to generate Ethereum accounts and sign data and transactions.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-eth-accounts", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "types": "types/index.d.ts", "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib  types"}, "main": "lib/index.js", "dependencies": {"@ethereumjs/common": "2.5.0", "@ethereumjs/tx": "3.3.2", "eth-lib": "0.2.8", "ethereumjs-util": "^7.1.5", "scrypt-js": "^3.0.1", "uuid": "^9.0.0", "web3-core": "1.8.2", "web3-core-helpers": "1.8.2", "web3-core-method": "1.8.2", "web3-utils": "1.8.2"}, "devDependencies": {"dtslint": "^3.4.1", "typescript": "4.1"}, "gitHead": "5442ce929ce1e2d33fd16afb0dd239a3d1f369f8"}