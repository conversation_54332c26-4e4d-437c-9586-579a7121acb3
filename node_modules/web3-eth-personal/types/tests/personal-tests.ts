/*
    This file is part of web3.js.
    web3.js is free software: you can redistribute it and/or modify
    it under the terms of the GNU Lesser General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.
    web3.js is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERC<PERSON><PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU Lesser General Public License for more details.
    You should have received a copy of the GNU Lesser General Public License
    along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
/**
 * @file personal-tests.ts
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @date 2018
 */

import { RLPEncodedTransaction } from 'web3-core';
import { Personal } from 'web3-eth-personal';

// $ExpectType Personal
const personal_empty = new Personal();

// $ExpectType Personal
const personal = new Personal('http://localhost:7545');

// $ExpectType string | null
personal.defaultAccount;

// $ExpectType string | number
personal.defaultBlock;

// $ExpectType provider
personal.currentProvider;

// $ExpectType any
Personal.givenProvider;

// $ExpectType any
personal.givenProvider;

// $ExpectType boolean
personal.setProvider('https://localhost:2100');

// $ExpectType BatchRequest
new personal.BatchRequest();

// $ExpectType any
personal.extend({property: 'test', methods: [{name: 'method', call: 'method'}]});

// $ExpectType Promise<string>
personal.newAccount('test password');
// $ExpectType Promise<string>
personal.newAccount('test password', (error: Error, address: string) => {});

// $ExpectType Promise<string>
personal.sign(
    'Hello world',
    '******************************************',
    'test password!'
);
// $ExpectType Promise<string>
personal.sign(
    'Hello world',
    '******************************************',
    'test password!',
    (error: Error, signature: string) => {}
);

// $ExpectType Promise<string>
personal.ecRecover('Hello world', '******************************************');
// $ExpectType Promise<string>
personal.ecRecover(
    'Hello world',
    '******************************************',
    (error: Error, address: string) => {}
);

// $ExpectType Promise<RLPEncodedTransaction>
personal.signTransaction(
    {
        from: '******************************************',
        gasPrice: '***********',
        gas: '21000',
        to: '0x3535353535353535353535353535353535353535',
        value: '1000000000000000000',
        data: ''
    },
    'test password'
);
// $ExpectType Promise<RLPEncodedTransaction>
personal.signTransaction(
    {
        from: '******************************************',
        gasPrice: '***********',
        gas: '21000',
        to: '0x3535353535353535353535353535353535353535',
        value: '1000000000000000000',
        data: ''
    },
    'test password',
    (error: Error, RLPEncodedTransaction: RLPEncodedTransaction) => {}
);

// $ExpectType Promise<string>
personal.sendTransaction(
    {
        from: '******************************************',
        gasPrice: '***********',
        gas: '21000',
        to: '0x3535353535353535353535353535353535353535',
        value: '1000000000000000000',
        data: ''
    },
    'test password'
);

// $ExpectType Promise<string>
personal.sendTransaction(
    {
        from: '******************************************',
        gasPrice: '***********',
        gas: '21000',
        to: '0x3535353535353535353535353535353535353535',
        value: '1000000000000000000',
        data: ''
    },
    'test password',
    (error: Error, transactionHash: string) => {}
);

// $ExpectType Promise<boolean>
personal.unlockAccount(
    '******************************************',
    'test password!',
    600
);
// $ExpectType Promise<boolean>
personal.unlockAccount(
    '******************************************',
    'test password!',
    600,
    (error: Error) => {}
);

// $ExpectType Promise<boolean>
personal.lockAccount('******************************************');
// $ExpectType Promise<boolean>
personal.lockAccount(
    '******************************************',
    (error: Error, sucess: boolean) => {}
);

// $ExpectType Promise<string[]>
personal.getAccounts();
// $ExpectType Promise<string[]>
personal.getAccounts((error: Error, accounts: string[]) => {});

// $ExpectType Promise<string>
personal.importRawKey('privateKey', 'blah2');
