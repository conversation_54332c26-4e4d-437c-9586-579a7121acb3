{"name": "bn.js", "version": "4.12.2", "description": "Big number implementation in pure javascript", "main": "lib/bn.js", "scripts": {"lint": "semistandard", "unit": "mocha --reporter=spec test/*-test.js", "test": "npm run lint && npm run unit"}, "repository": {"type": "git", "url": "**************:indutny/bn.js"}, "keywords": ["BN", "BigNum", "Big number", "<PERSON><PERSON><PERSON>", "<PERSON>"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/bn.js/issues"}, "homepage": "https://github.com/indutny/bn.js", "browser": {"buffer": false}, "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "semistandard": "^7.0.4"}}