{"name": "bs58check", "version": "2.1.2", "description": "A straightforward implementation of base58-check encoding", "keywords": ["base", "base58", "base58check", "bitcoin", "bs58", "check", "checksum", "decode", "decoding", "encode", "encoding", "litecoin"], "homepage": "https://github.com/bitcoinjs/bs58check", "bugs": {"url": "https://github.com/bitcoinjs/bs58check/issues"}, "license": "MIT", "author": "<PERSON>", "files": ["index.js", "base.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/bitcoinjs/bs58check.git"}, "scripts": {"coverage-report": "nyc report --reporter=lcov", "coverage": "nyc --check-coverage --branches 90 --functions 90 npm run unit", "standard": "standard", "test": "npm run standard && npm run coverage", "unit": "tape test/*.js"}, "dependencies": {"bs58": "^4.0.0", "create-hash": "^1.1.0", "safe-buffer": "^5.1.2"}, "devDependencies": {"blake-hash": "^1.0.0", "nyc": "^11.3.0", "standard": "^10.0.3", "tape": "^4.6.2"}}