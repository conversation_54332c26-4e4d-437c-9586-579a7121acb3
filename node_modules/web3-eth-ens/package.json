{"name": "web3-eth-ens", "version": "1.8.2", "description": "ENS support for web3.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-eth-ens", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "types": "types/index.d.ts", "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib  types"}, "main": "lib/index.js", "dependencies": {"content-hash": "^2.5.2", "eth-ens-namehash": "2.0.8", "web3-core": "1.8.2", "web3-core-helpers": "1.8.2", "web3-core-promievent": "1.8.2", "web3-eth-abi": "1.8.2", "web3-eth-contract": "1.8.2", "web3-utils": "1.8.2"}, "devDependencies": {"dtslint": "^3.4.1", "typescript": "4.1", "web3-eth": "1.8.2"}, "gitHead": "5442ce929ce1e2d33fd16afb0dd239a3d1f369f8"}